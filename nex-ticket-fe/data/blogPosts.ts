export interface BlogPost {
  id: number
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: Date
  category: 'events' | 'tips' | 'updates' | 'guides'
  slug: string
  featuredImage: string
  readTime: number
  tags: string[]
  featured?: boolean
  published?: boolean
}

// Sample blog posts - replace with your actual content management system
export const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: 'How to Create Unforgettable Events: A Complete Guide',
    excerpt: 'Learn the essential steps to planning and executing events that leave lasting impressions on your attendees. From initial concept to post-event follow-up.',
    content: `
      <h2>Introduction</h2>
      <p>Creating unforgettable events requires careful planning, attention to detail, and a deep understanding of your audience. In this comprehensive guide, we'll walk you through the essential steps to ensure your events leave lasting impressions.</p>

      <h2>1. Define Your Event Goals</h2>
      <p>Before diving into the logistics, it's crucial to establish clear objectives for your event. Ask yourself:</p>
      <ul>
        <li>What do you want to achieve?</li>
        <li>Who is your target audience?</li>
        <li>What experience do you want to create?</li>
        <li>How will you measure success?</li>
      </ul>

      <h2>2. Know Your Audience</h2>
      <p>Understanding your attendees is key to creating an event that resonates. Consider their preferences, expectations, and what would make the event valuable for them. Conduct surveys, analyze past event data, and create detailed attendee personas.</p>

      <h2>3. Choose the Right Venue</h2>
      <p>The venue sets the tone for your entire event. Consider factors like:</p>
      <ul>
        <li>Capacity and layout flexibility</li>
        <li>Location and accessibility</li>
        <li>Ambiance and atmosphere</li>
        <li>Technical capabilities and AV equipment</li>
        <li>Parking and transportation options</li>
      </ul>

      <h2>4. Create Engaging Content</h2>
      <p>Whether it's speakers, entertainment, or interactive activities, your content should align with your goals and audience interests. Mix different formats to keep attendees engaged throughout the event.</p>

      <h2>5. Pay Attention to Details</h2>
      <p>The small details often make the biggest difference. From registration flow to post-event follow-up, every touchpoint matters. Create detailed timelines and checklists to ensure nothing is overlooked.</p>

      <h2>6. Leverage Technology</h2>
      <p>Use event management platforms like TicketPie to streamline registration, ticketing, and attendee communication. Technology can significantly enhance the attendee experience and simplify event management.</p>

      <h2>Conclusion</h2>
      <p>Creating unforgettable events is both an art and a science. By following these guidelines and continuously learning from each event, you'll be well on your way to hosting memorable experiences that attendees will talk about long after they're over.</p>
    `,
    author: 'TicketPie Team',
    publishedAt: new Date('2025-01-15'),
    category: 'guides',
    slug: 'how-to-create-unforgettable-events',
    featuredImage: '/api/placeholder/800/400',
    readTime: 8,
    tags: ['event planning', 'tips', 'guide', 'best practices'],
    featured: true,
    published: true,
  },
  {
    id: 2,
    title: '10 Event Marketing Strategies That Actually Work',
    excerpt: 'Discover proven marketing techniques to boost attendance and engagement for your events. Learn from successful event organizers and marketing experts.',
    content: `
      <h2>Introduction</h2>
      <p>Marketing your event effectively is crucial for its success. Here are 10 proven strategies that will help you reach your target audience and boost attendance.</p>

      <h2>1. Start Early and Build Momentum</h2>
      <p>Begin marketing your event at least 6-8 weeks in advance. Create a timeline that builds excitement gradually, with key announcements and reveals along the way.</p>

      <h2>2. Leverage Social Media</h2>
      <p>Use multiple social media platforms to reach different audience segments. Create engaging content, use relevant hashtags, and encourage user-generated content.</p>

      <h2>3. Partner with Influencers</h2>
      <p>Collaborate with industry influencers and thought leaders who can help amplify your message to their engaged audiences.</p>

      <h2>4. Email Marketing</h2>
      <p>Build an email list and send targeted campaigns. Segment your audience for personalized messaging that resonates with different attendee types.</p>

      <h2>5. Content Marketing</h2>
      <p>Create valuable content related to your event theme. Blog posts, videos, and podcasts can establish your authority and attract potential attendees.</p>

      <h2>6. Early Bird Pricing</h2>
      <p>Offer discounted tickets for early registrations. This creates urgency and helps you gauge interest while generating early revenue.</p>

      <h2>7. Referral Programs</h2>
      <p>Encourage attendees to bring friends by offering incentives for referrals. Word-of-mouth marketing is incredibly powerful for events.</p>

      <h2>8. Local Media and PR</h2>
      <p>Reach out to local newspapers, radio stations, and bloggers. A well-crafted press release can generate significant coverage.</p>

      <h2>9. Community Partnerships</h2>
      <p>Partner with local businesses, organizations, and communities that share your target audience. Cross-promotion benefits everyone involved.</p>

      <h2>10. Retargeting Campaigns</h2>
      <p>Use digital advertising to retarget website visitors who haven't registered yet. Remind them about your event with compelling ads.</p>

      <h2>Conclusion</h2>
      <p>Successful event marketing requires a multi-channel approach and consistent effort. Start early, be authentic, and focus on providing value to your potential attendees.</p>
    `,
    author: 'Marketing Team',
    publishedAt: new Date('2025-01-10'),
    category: 'tips',
    slug: 'event-marketing-strategies-that-work',
    featuredImage: '/api/placeholder/800/400',
    readTime: 6,
    tags: ['marketing', 'promotion', 'attendance', 'social media'],
    featured: false,
    published: true,
  },
  {
    id: 3,
    title: 'TicketPie Platform Updates: January 2025',
    excerpt: 'Check out the latest features and improvements we\'ve made to enhance your event management experience. New tools for better attendee engagement and analytics.',
    content: `
      <h2>What's New This Month</h2>
      <p>We're excited to share the latest updates to the TicketPie platform. Our team has been working hard to bring you new features and improvements based on your feedback.</p>

      <h2>Enhanced Analytics Dashboard</h2>
      <p>Our new analytics dashboard provides deeper insights into your event performance:</p>
      <ul>
        <li>Real-time ticket sales tracking</li>
        <li>Attendee demographics breakdown</li>
        <li>Revenue forecasting tools</li>
        <li>Marketing campaign performance metrics</li>
      </ul>

      <h2>Improved Mobile Experience</h2>
      <p>We've completely redesigned our mobile interface to make event management on-the-go even easier. The new design is faster, more intuitive, and includes all desktop features.</p>

      <h2>Advanced Ticketing Options</h2>
      <p>New ticketing features include:</p>
      <ul>
        <li>Group discount tiers</li>
        <li>Dynamic pricing based on demand</li>
        <li>VIP package bundles</li>
        <li>Waitlist management</li>
      </ul>

      <h2>Integration Improvements</h2>
      <p>We've enhanced our integrations with popular tools:</p>
      <ul>
        <li>Mailchimp for email marketing</li>
        <li>Zoom for virtual events</li>
        <li>Stripe for payment processing</li>
        <li>Google Analytics for tracking</li>
      </ul>

      <h2>Security Enhancements</h2>
      <p>Your data security is our priority. This month we've implemented:</p>
      <ul>
        <li>Two-factor authentication</li>
        <li>Enhanced encryption protocols</li>
        <li>Regular security audits</li>
        <li>GDPR compliance improvements</li>
      </ul>

      <h2>Coming Next Month</h2>
      <p>Stay tuned for these upcoming features:</p>
      <ul>
        <li>AI-powered event recommendations</li>
        <li>Advanced networking tools</li>
        <li>Custom branding options</li>
        <li>Multi-language support expansion</li>
      </ul>

      <h2>Feedback Welcome</h2>
      <p>We love hearing from our users! If you have suggestions for future updates or need help with any of these new features, don't hesitate to reach out to our support team.</p>
    `,
    author: 'Product Team',
    publishedAt: new Date('2025-01-05'),
    category: 'updates',
    slug: 'platform-updates-january-2025',
    featuredImage: '/api/placeholder/800/400',
    readTime: 4,
    tags: ['updates', 'features', 'platform', 'analytics'],
    featured: false,
    published: true,
  },
  {
    id: 4,
    title: 'The Psychology of Event Experiences: Creating Emotional Connections',
    excerpt: 'Understand how psychology influences event experiences and learn to create deeper emotional connections with your attendees for lasting impact.',
    content: `
      <h2>The Power of Emotional Connection</h2>
      <p>Events that create strong emotional connections are the ones people remember and talk about for years. Understanding the psychology behind memorable experiences can transform your events from ordinary to extraordinary.</p>

      <h2>The Science of Memory and Events</h2>
      <p>Research shows that emotional experiences are more likely to be remembered. When attendees feel strong emotions during your event, they form lasting memories that influence their future behavior and loyalty.</p>

      <h2>Creating Anticipation</h2>
      <p>The experience begins before the event. Build anticipation through:</p>
      <ul>
        <li>Teaser content and behind-the-scenes glimpses</li>
        <li>Countdown campaigns</li>
        <li>Exclusive previews for early registrants</li>
        <li>Interactive pre-event activities</li>
      </ul>

      <h2>The Welcome Experience</h2>
      <p>First impressions matter enormously. Create a welcoming atmosphere that immediately sets the right tone and makes attendees feel valued and excited to be there.</p>

      <h2>Sensory Engagement</h2>
      <p>Engage all five senses to create immersive experiences:</p>
      <ul>
        <li>Visual: Lighting, colors, and visual displays</li>
        <li>Audio: Music, sound effects, and acoustics</li>
        <li>Touch: Interactive elements and materials</li>
        <li>Smell: Signature scents or food aromas</li>
        <li>Taste: Memorable food and beverage experiences</li>
      </ul>

      <h2>Social Connection</h2>
      <p>Humans are social beings. Facilitate connections through structured networking, interactive activities, and shared experiences that bring people together.</p>

      <h2>Surprise and Delight</h2>
      <p>Unexpected positive moments create peak experiences. Plan surprise elements that exceed expectations and create "wow" moments throughout your event.</p>

      <h2>Conclusion</h2>
      <p>By understanding and applying psychological principles, you can create events that not only inform or entertain but also create lasting emotional connections with your attendees.</p>
    `,
    author: 'Dr. Sarah J.',
    publishedAt: new Date('2025-01-01'),
    category: 'guides',
    slug: 'psychology-of-event-experiences',
    featuredImage: '/api/placeholder/800/400',
    readTime: 5,
    tags: ['psychology', 'experience design', 'attendee engagement', 'emotions'],
    featured: true,
    published: true,
  },
]

// Helper functions for blog management
export function getBlogPostBySlug(slug: string): BlogPost | undefined {
  return blogPosts.find(post => post.slug === slug && post.published)
}

export function getFeaturedPosts(): BlogPost[] {
  return blogPosts.filter(post => post.featured && post.published)
}

export function getPostsByCategory(category: string): BlogPost[] {
  if (category === 'all') {
    return blogPosts.filter(post => post.published)
  }
  return blogPosts.filter(post => post.category === category && post.published)
}

export function getRelatedPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {
  return blogPosts
    .filter(post =>
      post.id !== currentPost.id
      && post.published
      && (post.category === currentPost.category
        || post.tags.some(tag => currentPost.tags.includes(tag))),
    )
    .slice(0, limit)
}

export function getAllTags(): string[] {
  const allTags = blogPosts
    .filter(post => post.published)
    .flatMap(post => post.tags)

  return [...new Set(allTags)].sort()
}

export function getPostsByTag(tag: string): BlogPost[] {
  return blogPosts.filter(post =>
    post.published && post.tags.includes(tag),
  )
}
